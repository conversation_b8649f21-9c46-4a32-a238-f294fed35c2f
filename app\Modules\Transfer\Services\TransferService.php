<?php

namespace App\Modules\Transfer\Services;

use Illuminate\Support\Facades\DB;

class TransferService
{
    public static function instance(): self
    {
        return new self;
    }

    public function get(): array
    {
        return DB::table('transfer_domains')
            ->join('registered_domains', 'registered_domains.id', '=', 'transfer_domains.registered_domain_id')
            ->join('domains', 'domains.id', '=', 'registered_domains.domain_id')
            ->select([
                'transfer_domains.id',
                'transfer_domains.registered_domain_id',
                'transfer_domains.status',
                'transfer_domains.error_code',
                'transfer_domains.error_message',
                'transfer_domains.deleted_at',
                'transfer_domains.created_at',
                'transfer_domains.updated_at',
                'domains.name as domain',
            ])
            ->get()
            ->toArray();
    }
}
