import AdminLayout from "@/Layouts/AdminLayout";
import React, { useState } from "react";
import AppInputCheckboxComponent from "@/Components/App/AppInputCheckboxComponent";
import AppTableComponent from "@/Components/App/AppTableComponent";
import SecondaryButton from "@/Components/SecondaryButton";
import { toast } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

export default function Index({ items }) {
    const [stateTableItems] = useState(items);
    const [stateSelectedItems, setStateSelectedItems] = useState([]);

    function handleCheckAll(e) {
        if (e.target.checked) {
            setStateSelectedItems(stateTableItems.map((item) => item.id));
        } else {
            setStateSelectedItems([]);
        }
    }

    function handleCheck(e) {
        const { value, checked } = e.target;
        const id = parseInt(value);

        if (checked) {
            setStateSelectedItems([...stateSelectedItems, id]);
        } else {
            setStateSelectedItems(stateSelectedItems.filter((item) => item !== id));
        }
    }

    function checkIfAllRowsSelected(requiredItems, selectedItems) {
        return requiredItems.length > 0 && requiredItems.every((id) => selectedItems.includes(id));
    }

    function handleApproveSelected() {
        if (stateSelectedItems.length === 0) return;

        const selectedDomains = stateTableItems
            .filter((item) => stateSelectedItems.includes(item.id))
            .map((item) => item.domain)
            .join(", ");
        
        toast.success(`Domain successfully approved`);
        setStateSelectedItems([]);
    }

    const columnHeaders = [
        {
            value: (
                <AppInputCheckboxComponent
                    id="check_all"
                    name="check_all"
                    value="all"
                    isDisabled={false}
                    isChecked={checkIfAllRowsSelected(
                        stateTableItems.map((item) => item.id),
                        stateSelectedItems
                    )}
                    handleEventOnChange={handleCheckAll}
                />
            ),
        },
        {
            value: "Domain",
        },
    ];

    const columnRows = stateTableItems.map((row) => [
        {
            value: (
                <AppInputCheckboxComponent
                    id={row.id}
                    name={`check_${row.id}`}
                    value={row.id}
                    isDisabled={false}
                    isChecked={stateSelectedItems.includes(row.id)}
                    handleEventOnChange={handleCheck}
                />
            ),
        },
        {
            value: row.domain,
        },
    ]);

    return (
        <AdminLayout>
            <div className="mx-auto container max-w-[800px] mt-20 flex flex-col gap-6 px-5">
                <div className="flex justify-between items-center">
                    <h1 className="text-2xl font-semibold text-gray-900">
                        Domain Transfers
                    </h1>

                    <div className="flex items-center gap-4">
                        <SecondaryButton
                            onClick={handleApproveSelected}
                            disabled={stateSelectedItems.length === 0}
                            className={`flex items-center gap-2 ${
                                stateSelectedItems.length === 0 ? "opacity-50" : ""
                            }`}
                        >
                            Approve Selected
                        </SecondaryButton>
                    </div>
                </div>

                <AppTableComponent
                    pageRoute={"transfer.view"}
                    tableName={"transfers"}
                    columnHeaders={columnHeaders}
                    columnRows={columnRows}
                    shouldPreserveState={true}
                />
            </div>
        </AdminLayout>
    );
}
